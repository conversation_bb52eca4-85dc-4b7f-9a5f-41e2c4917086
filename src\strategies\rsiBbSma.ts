import { RSI, <PERSON>llingerBands, SMA } from 'technicalindicators'

export function evaluateRsiBbSma(candles: Candle[], config: RsiBbSmaConfig, lastSignal: Signal): SignalMeta {
	const closes = candles.map(c => c.close)

	const rsi = RSI.calculate({ period: config.rsiPeriod, values: closes })
	const bb = BollingerBands.calculate({ period: config.bbPeriod, stdDev: 2, values: closes })
	const sma = SMA.calculate({ period: config.smaPeriod, values: closes })

	const i = closes.length - 1
	const price = closes[i]
	const ri = rsi.length - 1,
		bi = bb.length - 1,
		si = sma.length - 1

	if (ri < 0 || bi < 0 || si < 0) {
		return { signal: undefined, reason: 'Not enough data for RSI/BB/SMA' }
	}

	const lastRSI = rsi[ri]
	const lastBB = bb[bi]
	const lastSMA = sma[si]

	// Check if any values are undefined before proceeding
	if (price == null || lastRSI == null || lastBB == null || lastSMA == null) {
		return { signal: undefined, reason: 'Incomplete indicator values' }
	}
	if (lastBB.lower == null || lastBB.upper == null) {
		return { signal: undefined, reason: 'Incomplete Bollinger Bands values' }
	}

	if (lastRSI < 30 && price < lastBB.lower && price > lastSMA) {
		if (lastSignal !== 'BUY') return { signal: 'BUY', reason: 'RSI oversold + price below BB + above SMA' }
	}

	if (lastRSI > 70 && price > lastBB.upper && price < lastSMA) {
		if (lastSignal !== 'SELL') return { signal: 'SELL', reason: 'RSI overbought + price above BB + below SMA' }
	}

	return { signal: 'HOLD', reason: 'Conditions not met or duplicate' }
}
