import { MACD, Stochastic } from 'technicalindicators'

export function evaluateMacdStoch(candles: Candle[]): Signal {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const macd = MACD.calculate({
		values: closes,
		fastPeriod: 12,
		slowPeriod: 26,
		signalPeriod: 9,
		SimpleMAOscillator: false,
		SimpleMASignal: false
	})

	const stoch = Stochastic.calculate({
		period: 14,
		signalPeriod: 3,
		high: highs,
		low: lows,
		close: closes
	})

	const mi = macd.length - 1
	const si = stoch.length - 1

	if (mi < 1 || si < 1) return undefined

	const lastMACD = macd[mi]
	const prevMACD = macd[mi - 1]
	const lastStoch = stoch[si]
	const prevStoch = stoch[si - 1]

	// Check if any values are undefined before proceeding
	if (lastMACD == null || prevMACD == null || lastStoch == null || prevStoch == null) return undefined
	if (lastMACD.MACD == null || lastMACD.signal == null || prevMACD.MACD == null || prevMACD.signal == null) return undefined
	if (lastStoch.k == null || lastStoch.d == null || prevStoch.k == null || prevStoch.d == null) return undefined

	// MACD bullish crossover + Stochastic oversold and turning up
	const macdBullishCross = prevMACD.MACD <= prevMACD.signal && lastMACD.MACD > lastMACD.signal
	const stochOversoldTurning = lastStoch.k < 30 && lastStoch.k > prevStoch.k

	// MACD bearish crossover + Stochastic overbought and turning down
	const macdBearishCross = prevMACD.MACD >= prevMACD.signal && lastMACD.MACD < lastMACD.signal
	const stochOverboughtTurning = lastStoch.k > 70 && lastStoch.k < prevStoch.k

	if (macdBullishCross && stochOversoldTurning) return 'BUY'
	if (macdBearishCross && stochOverboughtTurning) return 'SELL'

	return 'HOLD'
}
