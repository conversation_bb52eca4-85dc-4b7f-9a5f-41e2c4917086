import { CCI, Stochastic } from 'technicalindicators'

export function evaluateCciStoch(candles: Candle[]): Signal {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const cci = CCI.calculate({
		period: 14, // Shorter period for more sensitive CCI
		high: highs,
		low: lows,
		close: closes
	})

	const stoch = Stochastic.calculate({
		period: 21, // Longer period for smoother Stochastic
		signalPeriod: 5,
		high: highs,
		low: lows,
		close: closes
	})

	const ci = cci.length - 1
	const si = stoch.length - 1

	if (ci < 1 || si < 1) return undefined

	const lastCCI = cci[ci]
	const prevCCI = cci[ci - 1]
	const lastStoch = stoch[si]
	const prevStoch = stoch[si - 1]

	// Check if any values are undefined before proceeding
	if (lastCCI == null || prevCCI == null || lastStoch == null || prevStoch == null) return undefined
	if (lastStoch.k == null || lastStoch.d == null || prevStoch.k == null || prevStoch.d == null) return undefined

	// CCI leading signal + Stochastic confirmation
	const cciOversoldCross = prevCCI <= -100 && lastCCI > -100 // CCI crossing above oversold
	const stochOversold = lastStoch.k < 25 && lastStoch.k > prevStoch.k // Stochastic turning up from oversold

	const cciOverboughtCross = prevCCI >= 100 && lastCCI < 100 // CCI crossing below overbought
	const stochOverbought = lastStoch.k > 75 && lastStoch.k < prevStoch.k // Stochastic turning down from overbought

	if (cciOversoldCross && stochOversold) return 'BUY'
	if (cciOverboughtCross && stochOverbought) return 'SELL'

	return 'HOLD'
}
