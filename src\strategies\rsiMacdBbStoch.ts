import { RSI, MACD, BollingerBands, Stochastic } from 'technicalindicators'

export function evaluateRsiMacdBbStoch(
	candles: Candle[],
	config: RsiMacdBbStochConfig,
	lastSignal: Signal
): SignalMeta {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const rsi = RSI.calculate({ period: config.rsiPeriod, values: closes })
	const bb = BollingerBands.calculate({ period: config.bbPeriod, stdDev: 2, values: closes })
	const macd = MACD.calculate({
		values: closes,
		fastPeriod: config.macdFast,
		slowPeriod: config.macdSlow,
		signalPeriod: config.macdSignal,
		SimpleMAOscillator: false,
		SimpleMASignal: false
	})
	const stoch = Stochastic.calculate({
		period: config.stochPeriod,
		signalPeriod: config.stochSignal,
		high: highs,
		low: lows,
		close: closes
	})

	const i = closes.length - 1
	if (rsi.length < 1 || bb.length < 1 || macd.length < 2 || stoch.length < 2) {
		return { signal: undefined, reason: 'Insufficient indicator data' }
	}

	const price = closes[i]
	const lastRSI = rsi.at(-1)!
	const lastBB = bb.at(-1)!
	const lastMACD = macd.at(-1)!
	const lastStoch = stoch.at(-1)!

	// Check if any values are undefined before proceeding
	if (price == null || lastRSI == null || lastBB == null || lastMACD == null || lastStoch == null) {
		return { signal: undefined, reason: 'Incomplete indicator values' }
	}
	if (lastBB.lower == null || lastBB.upper == null) {
		return { signal: undefined, reason: 'Incomplete Bollinger Bands values' }
	}
	if (lastMACD.MACD == null || lastMACD.signal == null) {
		return { signal: undefined, reason: 'Incomplete MACD values' }
	}
	if (lastStoch.k == null || lastStoch.d == null) {
		return { signal: undefined, reason: 'Incomplete Stochastic values' }
	}

	if (lastRSI < 30 && price < lastBB.lower && lastMACD.MACD > lastMACD.signal && lastStoch.k < 20) {
		if (lastSignal !== 'BUY') return { signal: 'BUY', reason: 'RSI low + BB low + MACD bullish + Stoch low' }
	}

	if (lastRSI > 70 && price > lastBB.upper && lastMACD.MACD < lastMACD.signal && lastStoch.k > 80) {
		if (lastSignal !== 'SELL') return { signal: 'SELL', reason: 'RSI high + BB high + MACD bearish + Stoch high' }
	}

	return { signal: 'HOLD', reason: 'No crossover or duplicate signal' }
}
