import { MACD, BollingerBands, Stochastic } from 'technicalindicators'

export function evaluateMacdBbStoch(candles: Candle[]): Signal {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const macd = MACD.calculate({
		values: closes,
		fastPeriod: 12,
		slowPeriod: 26,
		signalPeriod: 9,
		SimpleMAOscillator: false,
		SimpleMASignal: false
	})

	const bb = BollingerBands.calculate({
		period: 20,
		stdDev: 2,
		values: closes
	})

	const stoch = Stochastic.calculate({
		period: 14,
		signalPeriod: 3,
		high: highs,
		low: lows,
		close: closes
	})

	const i = closes.length - 1
	const mi = macd.length - 1
	const bi = bb.length - 1
	const si = stoch.length - 1

	if (mi < 1 || bi < 0 || si < 1) return undefined

	const price = closes[i]
	const lastMACD = macd[mi]
	const prevMACD = macd[mi - 1]
	const lastBB = bb[bi]
	const lastStoch = stoch[si]
	const prevStoch = stoch[si - 1]

	// Check if any values are undefined before proceeding
	if (price == null || lastMACD == null || prevMACD == null || lastBB == null || lastStoch == null || prevStoch == null) return undefined
	if (lastMACD.MACD == null || lastMACD.signal == null || prevMACD.MACD == null || prevMACD.signal == null) return undefined
	if (lastStoch.k == null || lastStoch.d == null || prevStoch.k == null || prevStoch.d == null) return undefined

	// Triple confirmation for BUY: MACD bullish + price near lower BB + Stochastic oversold turning up
	const macdBullishCross = prevMACD.MACD <= prevMACD.signal && lastMACD.MACD > lastMACD.signal
	const nearLowerBB = price <= lastBB.lower * 1.01 // Allow 1% tolerance
	const stochOversoldTurning = lastStoch.k < 30 && lastStoch.k > prevStoch.k && lastStoch.k > lastStoch.d

	// Triple confirmation for SELL: MACD bearish + price near upper BB + Stochastic overbought turning down
	const macdBearishCross = prevMACD.MACD >= prevMACD.signal && lastMACD.MACD < lastMACD.signal
	const nearUpperBB = price >= lastBB.upper * 0.99 // Allow 1% tolerance
	const stochOverboughtTurning = lastStoch.k > 70 && lastStoch.k < prevStoch.k && lastStoch.k < lastStoch.d

	if (macdBullishCross && nearLowerBB && stochOversoldTurning) return 'BUY'
	if (macdBearishCross && nearUpperBB && stochOverboughtTurning) return 'SELL'

	return 'HOLD'
}
