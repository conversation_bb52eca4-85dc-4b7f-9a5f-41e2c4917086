import { MACD, KeltnerChannels } from 'technicalindicators'

export function evaluateMacdKc(candles: Candle[]): Signal {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const macd = MACD.calculate({
		values: closes,
		fastPeriod: 12,
		slowPeriod: 26,
		signalPeriod: 9,
		SimpleMAOscillator: false,
		SimpleMASignal: false
	})

	const kc = KeltnerChannels.calculate({
		maPeriod: 20,
		atrPeriod: 10,
		useSMA: false,
		multiplier: 2,
		high: highs,
		low: lows,
		close: closes
	})

	const i = closes.length - 1
	const mi = macd.length - 1
	const ki = kc.length - 1

	if (mi < 1 || ki < 0) return undefined

	const price = closes[i]
	const lastMACD = macd[mi]
	const prevMACD = macd[mi - 1]
	const lastKC = kc[ki]

	// Check if any values are undefined before proceeding
	if (price == null || lastMACD == null || prevMACD == null || lastKC == null) return undefined
	if (lastMACD.MACD == null || lastMACD.signal == null || prevMACD.MACD == null || prevMACD.signal == null) return undefined

	// MACD bullish crossover + price near or below lower Keltner Channel
	const macdBullishCross = prevMACD.MACD <= prevMACD.signal && lastMACD.MACD > lastMACD.signal
	const nearLowerKC = price <= lastKC.lower * 1.01 // Allow 1% tolerance

	// MACD bearish crossover + price near or above upper Keltner Channel  
	const macdBearishCross = prevMACD.MACD >= prevMACD.signal && lastMACD.MACD < lastMACD.signal
	const nearUpperKC = price >= lastKC.upper * 0.99 // Allow 1% tolerance

	if (macdBullishCross && nearLowerKC) return 'BUY'
	if (macdBearishCross && nearUpperKC) return 'SELL'

	return 'HOLD'
}
