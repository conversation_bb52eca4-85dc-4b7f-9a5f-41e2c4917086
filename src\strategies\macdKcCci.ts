import { KeltnerChannels, CCI, MACD } from 'technicalindicators'

export function evaluateMacdKcCci(candles: Candle[], config: MacdKcCciConfig, lastSignal: Signal): SignalMeta {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const kc = KeltnerChannels.calculate({
		maPeriod: config.kcPeriod,
		atrPeriod: 10,
		useSMA: false,
		multiplier: config.kcMultiplier,
		high: highs,
		low: lows,
		close: closes
	})

	const cci = CCI.calculate({
		period: config.cciPeriod,
		high: highs,
		low: lows,
		close: closes
	})

	const macd = MACD.calculate({
		values: closes,
		fastPeriod: config.macdFast,
		slowPeriod: config.macdSlow,
		signalPeriod: config.macdSignal,
		SimpleMAOscillator: false,
		SimpleMASignal: false
	})

	if (kc.length < 1 || cci.length < 1 || macd.length < 2) {
		return { signal: undefined, reason: 'Insufficient indicator data' }
	}

	const price = closes.at(-1)!
	const lastKC = kc.at(-1)!
	const lastCCI = cci.at(-1)!
	const lastMACD = macd.at(-1)!

	// Check if MACD values are defined before proceeding
	if (lastMACD.MACD == null || lastMACD.signal == null) {
		return { signal: undefined, reason: 'Incomplete MACD values' }
	}

	if (price < lastKC.lower && lastCCI < -100 && lastMACD.MACD > lastMACD.signal) {
		if (lastSignal !== 'BUY') return { signal: 'BUY', reason: 'Price below KC + CCI low + MACD bullish' }
	}

	if (price > lastKC.upper && lastCCI > 100 && lastMACD.MACD < lastMACD.signal) {
		if (lastSignal !== 'SELL') return { signal: 'SELL', reason: 'Price above KC + CCI high + MACD bearish' }
	}

	return { signal: 'HOLD', reason: 'Conditions not met or duplicate signal' }
}
