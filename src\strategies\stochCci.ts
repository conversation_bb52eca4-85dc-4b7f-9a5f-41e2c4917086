import { Stochastic, CCI } from 'technicalindicators'

export function evaluateStochCci(candles: Candle[]): Signal {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const stoch = Stochastic.calculate({
		period: 14,
		signalPeriod: 3,
		high: highs,
		low: lows,
		close: closes
	})

	const cci = CCI.calculate({
		period: 20,
		high: highs,
		low: lows,
		close: closes
	})

	const si = stoch.length - 1
	const ci = cci.length - 1

	if (si < 1 || ci < 1) return undefined

	const lastStoch = stoch[si]
	const prevStoch = stoch[si - 1]
	const lastCCI = cci[ci]
	const prevCCI = cci[ci - 1]

	// Check if any values are undefined before proceeding
	if (lastStoch == null || prevStoch == null || lastCCI == null || prevCCI == null) return undefined
	if (lastStoch.k == null || lastStoch.d == null || prevStoch.k == null || prevStoch.d == null) return undefined

	// Both oscillators oversold and turning up
	const stochOversoldTurning = lastStoch.k < 30 && lastStoch.k > prevStoch.k && lastStoch.k > lastStoch.d
	const cciOversoldTurning = lastCCI < -100 && lastCCI > prevCCI

	// Both oscillators overbought and turning down
	const stochOverboughtTurning = lastStoch.k > 70 && lastStoch.k < prevStoch.k && lastStoch.k < lastStoch.d
	const cciOverboughtTurning = lastCCI > 100 && lastCCI < prevCCI

	if (stochOversoldTurning && cciOversoldTurning) return 'BUY'
	if (stochOverboughtTurning && cciOverboughtTurning) return 'SELL'

	return 'HOLD'
}
