import { KeltnerChannels, Stochastic } from 'technicalindicators'

export function evaluateKcStoch(candles: Candle[]): Signal {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const kc = KeltnerChannels.calculate({
		maPeriod: 20,
		atrPeriod: 10,
		useSMA: false,
		multiplier: 2,
		high: highs,
		low: lows,
		close: closes
	})

	const stoch = Stochastic.calculate({
		period: 14,
		signalPeriod: 3,
		high: highs,
		low: lows,
		close: closes
	})

	const i = closes.length - 1
	const ki = kc.length - 1
	const si = stoch.length - 1

	if (ki < 0 || si < 1) return undefined

	const price = closes[i]
	const lastKC = kc[ki]
	const lastStoch = stoch[si]
	const prevStoch = stoch[si - 1]

	// Check if any values are undefined before proceeding
	if (price == null || lastKC == null || lastStoch == null || prevStoch == null) return undefined
	if (lastStoch.k == null || lastStoch.d == null || prevStoch.k == null || prevStoch.d == null) return undefined

	// Price near/below lower KC + Stochastic oversold and turning up
	const nearLowerKC = price <= lastKC.lower * 1.01 // Allow 1% tolerance
	const stochOversoldTurning = lastStoch.k < 30 && lastStoch.k > prevStoch.k && lastStoch.k > lastStoch.d

	// Price near/above upper KC + Stochastic overbought and turning down
	const nearUpperKC = price >= lastKC.upper * 0.99 // Allow 1% tolerance
	const stochOverboughtTurning = lastStoch.k > 70 && lastStoch.k < prevStoch.k && lastStoch.k < lastStoch.d

	if (nearLowerKC && stochOversoldTurning) return 'BUY'
	if (nearUpperKC && stochOverboughtTurning) return 'SELL'

	return 'HOLD'
}
