import { KeltnerChannels, CCI } from 'technicalindicators'

export function evaluateKcCci(candles: Candle[]): Signal {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const kc = KeltnerChannels.calculate({
		maPeriod: 20,
		atrPeriod: 10,
		useSMA: false,
		multiplier: 2,
		high: highs,
		low: lows,
		close: closes
	})

	const cci = CCI.calculate({
		period: 20,
		high: highs,
		low: lows,
		close: closes
	})

	const i = closes.length - 1
	const ki = kc.length - 1
	const ci = cci.length - 1

	if (ki < 0 || ci < 1) return undefined

	const price = closes[i]
	const lastKC = kc[ki]
	const lastCCI = cci[ci]
	const prevCCI = cci[ci - 1]

	// Check if any values are undefined before proceeding
	if (price == null || lastKC == null || lastCCI == null || prevCCI == null) return undefined

	// Price near/below lower KC + CCI oversold and turning up
	const nearLowerKC = price <= lastKC.lower * 1.01 // Allow 1% tolerance
	const cciOversoldTurning = lastCCI < -100 && lastCCI > prevCCI

	// Price near/above upper KC + CCI overbought and turning down
	const nearUpperKC = price >= lastKC.upper * 0.99 // Allow 1% tolerance
	const cciOverboughtTurning = lastCCI > 100 && lastCCI < prevCCI

	if (nearLowerKC && cciOversoldTurning) return 'BUY'
	if (nearUpperKC && cciOverboughtTurning) return 'SELL'

	return 'HOLD'
}
