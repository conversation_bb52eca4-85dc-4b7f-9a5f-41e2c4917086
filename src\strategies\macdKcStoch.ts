import { MACD, KeltnerChannels, Stochastic } from 'technicalindicators'

export function evaluateMacdKcStoch(candles: Candle[]): Signal {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const macd = MACD.calculate({
		values: closes,
		fastPeriod: 12,
		slowPeriod: 26,
		signalPeriod: 9,
		SimpleMAOscillator: false,
		SimpleMASignal: false
	})

	const kc = KeltnerChannels.calculate({
		maPeriod: 20,
		atrPeriod: 10,
		useSMA: false,
		multiplier: 2,
		high: highs,
		low: lows,
		close: closes
	})

	const stoch = Stochastic.calculate({
		period: 14,
		signalPeriod: 3,
		high: highs,
		low: lows,
		close: closes
	})

	const i = closes.length - 1
	const mi = macd.length - 1
	const ki = kc.length - 1
	const si = stoch.length - 1

	if (mi < 1 || ki < 0 || si < 1) return undefined

	const price = closes[i]
	const lastMACD = macd[mi]
	const prevMACD = macd[mi - 1]
	const lastKC = kc[ki]
	const lastStoch = stoch[si]
	const prevStoch = stoch[si - 1]

	// Check if any values are undefined before proceeding
	if (price == null || lastMACD == null || prevMACD == null || lastKC == null || lastStoch == null || prevStoch == null) return undefined
	if (lastMACD.MACD == null || lastMACD.signal == null || prevMACD.MACD == null || prevMACD.signal == null) return undefined
	if (lastStoch.k == null || lastStoch.d == null || prevStoch.k == null || prevStoch.d == null) return undefined

	// Triple confirmation for BUY: MACD bullish + price near lower KC + Stochastic oversold turning up
	const macdBullishCross = prevMACD.MACD <= prevMACD.signal && lastMACD.MACD > lastMACD.signal
	const nearLowerKC = price <= lastKC.lower * 1.02 // Allow 2% tolerance for KC
	const stochOversoldTurning = lastStoch.k < 25 && lastStoch.k > prevStoch.k && lastStoch.k > lastStoch.d

	// Triple confirmation for SELL: MACD bearish + price near upper KC + Stochastic overbought turning down
	const macdBearishCross = prevMACD.MACD >= prevMACD.signal && lastMACD.MACD < lastMACD.signal
	const nearUpperKC = price >= lastKC.upper * 0.98 // Allow 2% tolerance for KC
	const stochOverboughtTurning = lastStoch.k > 75 && lastStoch.k < prevStoch.k && lastStoch.k < lastStoch.d

	if (macdBullishCross && nearLowerKC && stochOversoldTurning) return 'BUY'
	if (macdBearishCross && nearUpperKC && stochOverboughtTurning) return 'SELL'

	return 'HOLD'
}
