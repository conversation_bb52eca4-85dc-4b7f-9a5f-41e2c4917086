import { Stochastic, MACD } from 'technicalindicators'

export function evaluateStochMacd(candles: Candle[]): Signal {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const stoch = Stochastic.calculate({
		period: 14,
		signalPeriod: 3,
		high: highs,
		low: lows,
		close: closes
	})

	const macd = MACD.calculate({
		values: closes,
		fastPeriod: 12,
		slowPeriod: 26,
		signalPeriod: 9,
		SimpleMAOscillator: false,
		SimpleMASignal: false
	})

	const si = stoch.length - 1
	const mi = macd.length - 1

	if (si < 1 || mi < 1) return undefined

	const lastStoch = stoch[si]
	const prevStoch = stoch[si - 1]
	const lastMACD = macd[mi]
	const prevMACD = macd[mi - 1]

	// Check if any values are undefined before proceeding
	if (lastStoch == null || prevStoch == null || lastMACD == null || prevMACD == null) return undefined
	if (lastStoch.k == null || lastStoch.d == null || prevStoch.k == null || prevStoch.d == null) return undefined
	if (lastMACD.MACD == null || lastMACD.signal == null || prevMACD.MACD == null || prevMACD.signal == null) return undefined

	// Stochastic oversold + MACD bullish momentum
	const stochOversoldCross = lastStoch.k < 30 && prevStoch.k <= prevStoch.d && lastStoch.k > lastStoch.d
	const macdBullishMomentum = lastMACD.MACD > lastMACD.signal && lastMACD.MACD > prevMACD.MACD

	// Stochastic overbought + MACD bearish momentum
	const stochOverboughtCross = lastStoch.k > 70 && prevStoch.k >= prevStoch.d && lastStoch.k < lastStoch.d
	const macdBearishMomentum = lastMACD.MACD < lastMACD.signal && lastMACD.MACD < prevMACD.MACD

	if (stochOversoldCross && macdBullishMomentum) return 'BUY'
	if (stochOverboughtCross && macdBearishMomentum) return 'SELL'

	return 'HOLD'
}
