import { BollingerBands, Stochastic } from 'technicalindicators'

export function evaluateBbStoch(candles: Candle[]): Signal {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const bb = BollingerBands.calculate({
		period: 20,
		stdDev: 2,
		values: closes
	})

	const stoch = Stochastic.calculate({
		period: 14,
		signalPeriod: 3,
		high: highs,
		low: lows,
		close: closes
	})

	const i = closes.length - 1
	const bi = bb.length - 1
	const si = stoch.length - 1

	if (bi < 0 || si < 1) return undefined

	const price = closes[i]
	const lastBB = bb[bi]
	const lastStoch = stoch[si]
	const prevStoch = stoch[si - 1]

	// Check if any values are undefined before proceeding
	if (price == null || lastBB == null || lastStoch == null || prevStoch == null) return undefined
	if (lastStoch.k == null || lastStoch.d == null || prevStoch.k == null || prevStoch.d == null) return undefined

	// Price touching lower BB + Stochastic oversold and %K crossing above %D
	const touchingLowerBB = price <= lastBB.lower * 1.005 // Allow 0.5% tolerance
	const stochOversoldCross = lastStoch.k < 30 && prevStoch.k <= prevStoch.d && lastStoch.k > lastStoch.d

	// Price touching upper BB + Stochastic overbought and %K crossing below %D
	const touchingUpperBB = price >= lastBB.upper * 0.995 // Allow 0.5% tolerance
	const stochOverboughtCross = lastStoch.k > 70 && prevStoch.k >= prevStoch.d && lastStoch.k < lastStoch.d

	if (touchingLowerBB && stochOversoldCross) return 'BUY'
	if (touchingUpperBB && stochOverboughtCross) return 'SELL'

	return 'HOLD'
}
