import { evaluateMacdKc<PERSON>ci } from '../strategies/macdKcCci'
import { evaluateRsiBbSma } from '../strategies/rsiBbSma'
import { evaluateRsiMacdBbStoch } from '../strategies/rsiMacdBbStoch'
import { evaluateSmaCross } from '../strategies/smaCross'
import { evaluateMacdKc } from '../strategies/macdKc'
import { evaluateMacdStoch } from '../strategies/macdStoch'
import { evaluateBbStoch } from '../strategies/bbStoch'
import { evaluateKcStoch } from '../strategies/kcStoch'
import { evaluateKcCci } from '../strategies/kcCci'
import { evaluateStochCci } from '../strategies/stochCci'
import { evaluateCciStoch } from '../strategies/cciStoch'
import { evaluateStochMacd } from '../strategies/stochMacd'
import { evaluateMacdBbStoch } from '../strategies/macdBbStoch'
import { evaluateMacdKcStoch } from '../strategies/macdKcStoch'

// Default configurations for each strategy
const defaultStrategyConfig: Record<StrategyName, StrategyConfig> = {
	SMA_CROSS: {
		fastPeriod: 10,
		slowPeriod: 20
	} as SmaCrossConfig,
	RSI_BB_SMA: {
		rsiPeriod: 14,
		bbPeriod: 20,
		smaPeriod: 20
	} as RsiBbSmaConfig,
	RSI_MACD_BB_STOCH: {
		rsiPeriod: 14,
		bbPeriod: 20,
		macdFast: 12,
		macdSlow: 26,
		macdSignal: 9,
		stochPeriod: 14,
		stochSignal: 3
	} as RsiMacdBbStochConfig,
	MACD_KC_CCI: {
		macdFast: 12,
		macdSlow: 26,
		macdSignal: 9,
		kcPeriod: 20,
		kcMultiplier: 2,
		cciPeriod: 20
	} as MacdKcCciConfig,
	// Two-indicator strategies (using empty config as they have hardcoded values)
	MACD_KC: {},
	MACD_STOCH: {},
	BB_STOCH: {},
	KC_STOCH: {},
	KC_CCI: {},
	STOCH_CCI: {},
	CCI_STOCH: {},
	STOCH_MACD: {},
	// Three-indicator strategies (using empty config as they have hardcoded values)
	MACD_BB_STOCH: {},
	MACD_KC_STOCH: {}
}

// Wrapper functions for strategies that don't take config parameters
const wrapStrategy = (fn: (candles: Candle[]) => Signal): StrategyFn => {
	return (candles: Candle[], _config: StrategyConfig, lastSignal: Signal): SignalMeta => {
		const signal = fn(candles)
		if (signal === lastSignal) {
			return { signal: 'HOLD', reason: 'No change from last signal', confidence: 'low' }
		}
		return {
			signal,
			reason:
				signal === 'BUY' ? 'Buy signal detected' : signal === 'SELL' ? 'Sell signal detected' : 'Hold signal detected'
		}
	}
}

// Wrapper functions for strategies that take specific config types
const wrapConfigStrategy = <T>(fn: (candles: Candle[], config: T, lastSignal: Signal) => SignalMeta): StrategyFn => {
	return (candles: Candle[], config: StrategyConfig, lastSignal: Signal): SignalMeta => {
		return fn(candles, config as T, lastSignal)
	}
}

export const strategyMap: Record<StrategyName, StrategyFn> = {
	SMA_CROSS: wrapConfigStrategy<SmaCrossConfig>(evaluateSmaCross),
	RSI_BB_SMA: wrapConfigStrategy<RsiBbSmaConfig>(evaluateRsiBbSma),
	RSI_MACD_BB_STOCH: wrapConfigStrategy<RsiMacdBbStochConfig>(evaluateRsiMacdBbStoch),
	MACD_KC_CCI: wrapConfigStrategy<MacdKcCciConfig>(evaluateMacdKcCci),
	// Two-indicator strategies
	MACD_KC: wrapStrategy(evaluateMacdKc),
	MACD_STOCH: wrapStrategy(evaluateMacdStoch),
	BB_STOCH: wrapStrategy(evaluateBbStoch),
	KC_STOCH: wrapStrategy(evaluateKcStoch),
	KC_CCI: wrapStrategy(evaluateKcCci),
	STOCH_CCI: wrapStrategy(evaluateStochCci),
	CCI_STOCH: wrapStrategy(evaluateCciStoch),
	STOCH_MACD: wrapStrategy(evaluateStochMacd),
	// Three-indicator strategies
	MACD_BB_STOCH: wrapStrategy(evaluateMacdBbStoch),
	MACD_KC_STOCH: wrapStrategy(evaluateMacdKcStoch)
}

export class SignalEngine {
	private readonly candles: Candle[] = []
	private readonly strategyFn: (candles: Candle[], config: StrategyConfig, lastSignal: Signal) => SignalMeta
	private readonly config: StrategyConfig
	private readonly signalHistory: SignalMeta[] = []
	private lastSignal: Signal = undefined
	private lastMeta: SignalMeta = { signal: undefined, reason: 'Not evaluated yet', confidence: 'low' }

	constructor(strategyName: StrategyName, config?: Partial<StrategyConfig>) {
		const fn = strategyMap[strategyName]
		if (!fn) throw new Error(`Unknown strategy: ${strategyName}`)
		this.strategyFn = fn
		this.config = { ...defaultStrategyConfig[strategyName], ...config }
	}

	update(candle: Candle): SignalMeta {
		this.candles.push(candle)
		const result = this.strategyFn(this.candles, this.config, this.lastSignal)
		if (result.signal !== this.lastSignal) {
			this.lastSignal = result.signal
		} else {
			result.signal = 'HOLD'
			result.reason += ' (no change)'
		}
		this.lastMeta = result
		this.signalHistory.push(result)
		return result
	}

	getLastSignal(): Signal {
		return this.lastSignal
	}

	getLastMeta(): SignalMeta {
		return this.lastMeta
	}

	getSignalHistory(): SignalMeta[] {
		return this.signalHistory
	}

	reset(): void {
		this.candles.length = 0
		this.signalHistory.length = 0
		this.lastSignal = undefined
		this.lastMeta = { signal: undefined, reason: 'Reset', confidence: 'low' }
	}

	evaluatePerformance(correctSignals: Signal[], actualOutcomes: Signal[]): { accuracy: number } {
		let correct = 0
		const count = Math.min(correctSignals.length, actualOutcomes.length)
		for (let i = 0; i < count; i++) {
			if (correctSignals[i] === actualOutcomes[i]) correct++
		}
		return { accuracy: count > 0 ? correct / count : 0 }
	}

	static resolvePrioritySignal(signals: SignalMeta[], priorities: number[]): SignalMeta {
		let bestIndex = -1
		for (let i = 0; i < signals.length; i++) {
			const signal = signals[i]
			if (!signal || signal.signal === 'HOLD') continue
			if (
				bestIndex === -1 ||
				priorities[i] > priorities[bestIndex] ||
				(priorities[i] === priorities[bestIndex] && signals[i].signal !== 'HOLD')
			) {
				bestIndex = i
			}
		}
		return bestIndex >= 0 ? signals[bestIndex]! : { signal: 'HOLD', reason: 'No dominant signal', confidence: 'low' }
	}
}
